import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { DeliverableEntity } from '../entities/deliverable.entity';
import { BaseRepository } from './base.repository';

@Injectable()
export class DeliverableRepository extends BaseRepository<DeliverableEntity> {
  constructor(
    @InjectRepository(DeliverableEntity)
    public repository: Repository<DeliverableEntity>
  ) {
    super(repository);
  }

  async findByUid(uid: string): Promise<DeliverableEntity | null> {
    return this.repository.findOne({
      where: { uid },
      relations: ['deliverableType', 'deliverables']
    });
  }

  async findCompactStandaloneWithFilters(
    search?: string,
    functions?: string[],
    isActive?: boolean,
    types?: string[]
  ): Promise<Array<object>> {
    const query = this.repository.createQueryBuilder('deliverable').leftJoin('deliverable.deliverableType', 'deliverable_type');

    if (isActive !== undefined) {
      query.andWhere('deliverable.isActive = :isActive', {
        isActive: isActive ? 1 : 0
      });
    } else {
      query.andWhere('deliverable.isActive = 1');
    }

    if (search) {
      query.andWhere('deliverable.name LIKE :search', {
        search: `%${search}%`
      });
    }

    if (types && types.length > 0) {
      query.andWhere('deliverable.businessFunction IN (:...types)', {
        types
      });
    }

    if (functions && functions.length > 0) {
      query.andWhere('deliverable.deliverable_type.code IN (:...functions)', {
        functions
      });
    }

    query.orderBy('deliverable.name', 'ASC');

    const entities = await query
      .select([
        'deliverable.uid',
        'deliverable.name',
        'deliverable.businessFunction',
        'deliverable_type.code',
        'deliverable.isActive',
        'deliverable.calculationMethod',
        'deliverable.paValue',
        'deliverable.definition'
      ])
      .addSelect('deliverable_type.code', 'type')
      .getMany();

    return entities?.map(({ deliverableType, ...props }) => ({ ...props, type: deliverableType?.code }));
  }

  async findByUids(uids: string[]): Promise<DeliverableEntity[]> {
    if (!uids || uids.length === 0) {
      return [];
    }

    return this.repository
      .createQueryBuilder('deliverable')
      .select(['deliverable.uid', 'deliverable.name'])
      .where('deliverable.uid IN (:...uids)', { uids })
      .getMany();
  }

  async getFunctions(): Promise<{ function: string }[]> {
    const result = await this.repository
      .createQueryBuilder('deliverable')
      .select('deliverable.businessFunction', 'function')
      .where('deliverable.businessFunction IS NOT NULL')
      .andWhere('deliverable.businessFunction != :emptyString', { emptyString: '' })
      .distinct(true)
      .getRawMany();

    console.log('Functions retrieved:', result);
    return result;
  }
}
