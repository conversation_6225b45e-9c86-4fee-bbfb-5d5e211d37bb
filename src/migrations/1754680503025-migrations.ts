import { MigrationInterface, QueryRunner } from "typeorm";

export class Migrations1754680503025 implements MigrationInterface {
    name = 'Migrations1754680503025'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "deliverables" ("uid" uniqueidentifier NOT NULL CONSTRAINT "DF_63ff64b27aa42959f3daa527a3e" DEFAULT NEWID(), "deletedAt" datetime2, "createdAt" datetime2 NOT NULL CONSTRAINT "DF_8de66953734c2f53cba2eae4f8a" DEFAULT getdate(), "updatedAt" datetime2 CONSTRAINT "DF_43b1021ac6db2004d6992ab9ffb" DEFAULT getdate(), "deletedBy" uniqueidentifier, "createdBy" uniqueidentifier NOT NULL, "updatedBy" uniqueidentifier, "buLevelAggregation" nvarchar(MAX), "businessFunction" nvarchar(255) NOT NULL, "calculationMethod" nvarchar(MAX) NOT NULL, "content" nvarchar(MAX), "dateEnd" datetime2, "dateStart" datetime2, "definition" nvarchar(MAX) NOT NULL, "frequency" nvarchar(255), "isActive" bit NOT NULL, "name" nvarchar(255) NOT NULL, "paValue" nvarchar(MAX), "deliverable_type_code" nvarchar(255), "parent_deliverable_uid" uniqueidentifier, CONSTRAINT "PK_63ff64b27aa42959f3daa527a3e" PRIMARY KEY ("uid"))`);
        await queryRunner.query(`CREATE TABLE "deliverables_type" ("code" nvarchar(255) NOT NULL, CONSTRAINT "PK_1a8bd3164110ecf0b7f2b04c67b" PRIMARY KEY ("code"))`);
        await queryRunner.query(`ALTER TABLE "deliverables" ADD CONSTRAINT "FK_34aaf350a9e17cdbf028417dfb3" FOREIGN KEY ("deliverable_type_code") REFERENCES "deliverables_type"("code") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "deliverables" ADD CONSTRAINT "FK_8bbc1ef71a84ed1bf5138d15065" FOREIGN KEY ("parent_deliverable_uid") REFERENCES "deliverables"("uid") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "deliverables" DROP CONSTRAINT "FK_8bbc1ef71a84ed1bf5138d15065"`);
        await queryRunner.query(`ALTER TABLE "deliverables" DROP CONSTRAINT "FK_34aaf350a9e17cdbf028417dfb3"`);
        await queryRunner.query(`DROP TABLE "deliverables_type"`);
        await queryRunner.query(`DROP TABLE "deliverables"`);
    }

}
