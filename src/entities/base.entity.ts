import { Column, CreateDateColumn, DeleteDateColumn, PrimaryColumn, UpdateDateColumn } from 'typeorm';

export abstract class BaseEntity {
  @PrimaryColumn({ type: 'uniqueidentifier', default: () => 'NEWID()' })
  uid: string;

  @DeleteDateColumn({ type: 'datetime2', nullable: true })
  deletedAt?: Date;

  @CreateDateColumn({ type: 'datetime2' })
  createdAt: Date;

  @UpdateDateColumn({ type: 'datetime2', nullable: true })
  updatedAt?: Date;

  @Column({ type: 'uniqueidentifier', nullable: true })
  deletedBy?: string;

  @Column({ type: 'uniqueidentifier' })
  createdBy: string;

  @Column({ type: 'uniqueidentifier', nullable: true })
  updatedBy?: string;
}
