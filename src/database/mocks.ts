export const mockData = [
  {
    name: 'AI Touchpoints / Personalization Active With BU',
    deliverableType: {
      code: 'KPI'
    },
    function: 'TECHNOLOGY',
    frequency: 'Annualy',
    usage: 48,
    definition: 'Tracks the percentage of AI Touchpoints + Personalization + Data.',
    buLevelAggregation: 'buLevelAggregation',
    calculationMethod:
      '% AI Touchpoints + Personalization + Data Into Insights scope as part of the KPI as per BGT aligned with SteerCo',
    paValue: '100%:100% / 90%: 99% - 90% / 80%: 89%-80%',
    owners: [
      {
        email: '<EMAIL>',
        name: '<PERSON><PERSON><PERSON>',
        globalId: 99824775
      }
    ],
    isActive: true
  },
  {
    name: 'AI Touchpoints / Personalization Inactive With BU',
    deliverableType: {
      code: 'KPI'
    },
    function: 'TECHNOLOGY',
    frequency: 'Annualy',
    usage: 48,
    definition: 'Tracks the percentage of AI Touchpoints + Personalization + Data.',
    buLevelAggregation: 'buLevelAggregation',
    calculationMethod:
      '% AI Touchpoints + Personalization + Data Into Insights scope as part of the KPI as per BGT aligned with SteerCo',
    paValue: '100%:100% / 90%: 99% - 90% / 80%: 89%-80%',
    owners: [
      {
        email: '<EMAIL>',
        name: 'Afonso Lopez',
        globalId: 99824775
      }
    ],
    isActive: false
  },
  {
    name: 'AI Touchpoints / Personalization Without BU',
    deliverableType: {
      code: 'KPI'
    },
    function: 'TECHNOLOGY',
    frequency: 'Annualy',
    usage: 48,
    definition: 'Tracks the percentage of AI Touchpoints + Personalization + Data.',
    buLevelAggregation: null,
    calculationMethod:
      '% AI Touchpoints + Personalization + Data Into Insights scope as part of the KPI as per BGT aligned with SteerCo',
    paValue: '100%:100% / 90%: 99% - 90% / 80%: 89%-80%',
    owners: [
      {
        email: '<EMAIL>',
        name: 'Afonso Lopez',
        globalId: 99824775
      }
    ],
    isActive: false
  },
  {
    name: 'Digital Trade rollout Active',
    content: 'Deliver Digital Trade rollout (B4T Rehaul, Loyalty, Draught, etc)',
    deliverableType: {
      code: 'PROJECT_YES/NO'
    },
    function: 'MARKETING',
    frequency: 'Monthly',
    usage: 3,
    definition: 'Deliver Digital Trade rollout (B4T Rehaul, Loyalty, Draught, etc)',
    buLevelAggregation: null,
    calculationMethod: 'Deliver Digital Trade rollout (B4T Rehaul, Loyalty, Draught, etc)',
    paValue: null,
    isActive: true
  },
  {
    name: 'Digital Trade rollout Inactive',
    deliverableType: {
      code: 'PROJECT_YES/NO'
    },
    function: 'MARKETING',
    frequency: 'Monthly',
    usage: 3,
    definition: 'Deliver Digital Trade rollout (B4T Rehaul, Loyalty, Draught, etc)',
    buLevelAggregation: null,
    calculationMethod: 'Deliver Digital Trade rollout (B4T Rehaul, Loyalty, Draught, etc)',
    paValue: null,
    isActive: false
  },
  {
    name: 'Digital Trade rollout Scoped',
    deliverableType: {
      code: 'SCOPED_PROJECT_YES/NO'
    },
    function: 'MARKETING',
    frequency: 'Monthly',
    usage: 3,
    definition: 'Deliver Digital Trade rollout (B4T Rehaul, Loyalty, Draught, etc)',
    buLevelAggregation: null,
    calculationMethod: 'Deliver Digital Trade rollout (B4T Rehaul, Loyalty, Draught, etc)',
    paValue: null,
    isActive: true
  },
  {
    definition: 'Deliver Digital Trade rollout (B4T Rehaul, Loyalty, Draught, etc)',
    calculationMethod: 'Deliver Digital Trade rollout (B4T Rehaul, Loyalty, Draught, etc)',
    buLevelAggregation: null,
    deliverableType: {
      code: 'PROJECT'
    },
    name: 'People Transformation',
    function: 'PEOPLE',
    frequency: 'Monthly',
    isActive: true,
    usage: 69,
    deliverables: [
      {
        name: 'Digital Trade rollout Scoped',
        deliverableType: {
          code: 'SCOPED_PROJECT_YES/NO'
        },
        function: 'MARKETING',
        frequency: 'Monthly',
        usage: 3,
        definition: 'Deliver Digital Trade rollout (B4T Rehaul, Loyalty, Draught, etc)',
        buLevelAggregation: null,
        calculationMethod: 'Deliver Digital Trade rollout (B4T Rehaul, Loyalty, Draught, etc)',
        paValue: null,
        isActive: true
      },
      {
        name: 'AI Touchpoints / Personalization Active With BU',
        deliverableType: {
          code: 'KPI'
        },
        function: 'TECHNOLOGY',
        frequency: 'Annualy',
        usage: 48,
        definition: 'Tracks the percentage of AI Touchpoints + Personalization + Data.',
        buLevelAggregation: 'buLevelAggregation',
        calculationMethod:
          '% AI Touchpoints + Personalization + Data Into Insights scope as part of the KPI as per BGT aligned with SteerCo',
        paValue: '100%:100% / 90%: 99% - 90% / 80%: 89%-80%',
        owners: [
          {
            email: '<EMAIL>',
            name: 'Afonso Lopez',
            globalId: 99824775
          }
        ],
        isActive: true
      }
    ],
    paValue: null,
    date_start: new Date('2025-01-01'),
    date_end: new Date('2025-12-31')
  }
];
