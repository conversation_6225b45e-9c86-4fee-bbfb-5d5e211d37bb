import { Entity, Column, ManyToOne, OneToMany, Join<PERSON>olumn } from 'typeorm';
import { DeliverableTypeEntity } from './deliverable-type.entity';
import { BaseEntity } from './base.entity';

@Entity({ name: 'deliverables' })
export class DeliverableEntity extends BaseEntity {
  @Column({ type: 'nvarchar', length: 'MAX', nullable: true })
  buLevelAggregation: string;

  @Column()
  businessFunction: string;

  @Column({ type: 'nvarchar', length: 'MAX' })
  calculationMethod: string;

  @Column({ type: 'nvarchar', length: 'MAX', nullable: true })
  content: string;

  @Column({ type: 'datetime2', nullable: true })
  dateEnd: Date;

  @Column({ type: 'datetime2', nullable: true })
  dateStart: Date;

  @Column({ type: 'nvarchar', length: 'MAX' })
  definition: string;

  @Column({ type: 'nvarchar', nullable: true })
  frequency: string;

  @Column()
  isActive: boolean;

  @Column()
  name: string;

  @Column({ type: 'nvarchar', length: 'MAX', nullable: true })
  paValue: string;

  @ManyToOne(() => DeliverableTypeEntity, (type) => type.deliverables)
  @JoinColumn({ name: 'deliverable_type_code' })
  deliverableType: DeliverableTypeEntity;

  @OneToMany(() => DeliverableEntity, (deliverable) => deliverable.parentDeliverable)
  deliverables: DeliverableEntity[];

  @ManyToOne(() => DeliverableEntity, (deliverable) => deliverable.deliverables)
  @JoinColumn({ name: 'parent_deliverable_uid' })
  parentDeliverable: DeliverableEntity;

  itemType: 'Deliverable';
}
