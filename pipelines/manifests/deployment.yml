apiVersion: batch/v1
kind: Job
metadata:
  name: northstar-domain-$(Build.BuildId)
  namespace: $(k8sNamespace)
spec:
  completions: 1
  parallelism: 1
  template:
    spec:
      volumes:
        - name: cache-storage
          emptyDir: {}
        - name: yarn-global
          emptyDir: {}
        - name: tmp-storage
          emptyDir: {}
      containers:
        - name: northstar-domain
          volumeMounts:
            - name: cache-storage
              mountPath: /.cache
            - name: yarn-global
              mountPath: /.yarn
            - name: tmp-storage
              mountPath: /tmp
          image: peopleproductsacr.azurecr.io/northstar-domain:$(Build.BuildId)
          imagePullPolicy: Always
          env:
            - name: YARN_GLOBAL_FOLDER
              value: "/.yarn"
            - name: YARN_CACHE_FOLDER
              value: "/.cache"
            - name: DATABASE_HOST
              value: db-host@azurekeyvault
            - name: DATABASE_PORT
              value: db-port@azurekeyvault
            - name: DATABASE_USERNAME
              value: '$(dbUser)@azurekeyvault'
            - name: DATABASE_PASSWORD
              value: '$(dbPass)@azurekeyvault'
            - name: DATABASE_NAME
              value: db-name@azurekeyvault
            - name: DATABASE_SCHEMA
              value: 'dbo'
          resources:
            requests:
              memory: "256Mi"
              cpu: "100m"
            limits:
              memory: "512Mi"
              cpu: "500m"
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          securityContext:
            runAsUser: 10000
            allowPrivilegeEscalation: false
            runAsNonRoot: true
            readOnlyRootFilesystem: true
            privileged: false
            capabilities:
              drop:
                - ALL
      restartPolicy: Never
      terminationGracePeriodSeconds: 30
      dnsPolicy: ClusterFirst
      securityContext: {}
      imagePullSecrets:
        - name: $(imagePullSecrets)
      schedulerName: default-scheduler
  backoffLimit: 1
